/* ReplyPal - Animations and Loading States */

/* Loading Spinner */
.loading-spinner {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.loading-spinner.visible {
  display: block;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(74, 108, 247, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

/* Loading Indicator for Generate Button */
.generate-btn-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
  display: none;
}

.generate-btn-spinner.visible {
  display: inline-block;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 108, 247, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
  }
}

/* Apply animations to elements */
.tab-content.active {
  animation: fadeIn 0.3s ease;
}

.response-container:not(.hidden) {
  animation: slideUp 0.3s ease;
}

/* Typing indicator for streaming responses */
.typing-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 5px;
  position: relative;
}

.typing-indicator span {
  display: inline-block;
  width: 3px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  animation: typing 1s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  left: 0;
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(2) {
  left: 4px;
  animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
  left: 8px;
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
}

/* Highlight animation for new content */
@keyframes highlight {
  0% {
    background-color: rgba(74, 108, 247, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

.highlight {
  animation: highlight 1.5s ease;
}

/* Button hover effects */
.primary-btn:hover,
.secondary-btn:hover,
.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.primary-btn:active,
.secondary-btn:active,
.action-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

/* Transition for response text area */
#response-text {
  transition: height 0.2s ease;
}

/* Smooth transition for showing/hiding elements */
.form-group,
.control-group,
.response-container {
  transition: opacity 0.3s ease, height 0.3s ease, margin 0.3s ease;
}

/* Transition for edit mode */
#response-text.editing {
  background-color: #fff;
  border: 1px solid var(--primary-color);
}

/* Pulse animation for generate button */
#generate-btn:not(:disabled):not(:active):hover {
  animation: pulse 1.5s infinite;
}
