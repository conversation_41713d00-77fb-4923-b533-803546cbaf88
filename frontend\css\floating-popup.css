/* ReplyPal - Floating Popup Styles */

.replypal-floating-popup {
  position: fixed;
  top: 50px;
  left: 20px;
  min-width: 400px;
  min-height: 300px;
  width: 480px; /* Fixed width */
  height: 500px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 2147483647;
  overflow: auto;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  opacity: 0;
  transition: opacity 0.3s ease;
  resize: none; /* Disable resizing */
}

.replypal-floating-popup.visible {
  opacity: 1;
  animation: fadeIn 0.3s ease forwards;
}

.replypal-popup-drag-handle {
  cursor: move;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.replypal-popup-content {
  flex: 1;
  overflow: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 0;
}

.replypal-popup-iframe {
  width: 100%;
  height: 100%;
  min-width: 200px;
  min-height: 100px;
  border: none;
  overflow: auto;
  resize: none;
  display: block;
}

/* Floating Icon */
.replypal-icon {
  position: absolute;
  min-width: 95px;
  height: 30px;
  padding: 0 12px;
  background-color: #4285F4; /* Lighter blue color similar to the image */
  border-radius: 4px; /* Squared corners like in the image */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 2147483646;
  transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  letter-spacing: 0.2px;
  gap: 5px; /* Space between icon and text */
}

.replypal-icon:hover {
  transform: scale(1.05);
  background-color: #5294FF; /* Slightly lighter on hover */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
}

.replypal-icon-text {
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
  margin-top: 1px; /* Slight vertical alignment adjustment */
}

.replypal-icon-bubble {
  margin-right: 1px;
  width: 16px;
  height: 16px;
}

/* Remove old image styles */
.replypal-icon img {
  display: none;
}

/* Tooltip */
.replypal-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  white-space: nowrap;
  z-index: 2147483645;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transform: translateY(5px);
}

.replypal-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Add tooltip arrow */
.replypal-tooltip:after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  margin-left: -4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.75);
}

/* Media Queries for Responsive Design */
@media (max-width: 500px) {
  .replypal-floating-popup {
    width: 90vw;
    left: 5vw;
    right: 5vw;
  }
}

/* Animation for popup appearance */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
