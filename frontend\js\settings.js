/**
 * ReplyPal - Settings Management
 * Handles loading, saving, and managing settings
 */

// DOM Elements
const environmentSelect = document.getElementById('environment');
const currentApiUrlSpan = document.getElementById('current-api-url');
const testApiBtn = document.getElementById('test-api-btn');
const apiTestResult = document.getElementById('api-test-result');
const saveHistoryCheckbox = document.getElementById('save-history');
const useMockApiCheckbox = document.getElementById('use-mock-api');
const saveSettingsBtn = document.getElementById('save-settings-btn');
const apiSettingsHeading = document.querySelector('#settings .settings-container h3:first-of-type');
const apiSettingsFormGroup = document.querySelector('#settings .settings-container .form-group:first-of-type');
const toggleHotkeysBtn = document.getElementById('toggle-hotkeys-btn');
const additionalHotkeys = document.getElementById('additional-hotkeys');

// Default settings
const defaultSettings = {
  environment: 'prod', //local, dev, prod
  apiUrl: 'http://localhost:8000',
  saveHistory: true,
  useMockApi: false
};

// API URLs for different environments
const apiUrls = {
  local: 'http://localhost:8000/generate_stream',
  dev: 'https://dev-api.replypal.com',
  prod: 'https://api.replypal.com'
};

// Set up event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Call toggleApiSettingsVisibility on page load
  toggleApiSettingsVisibility();

  // Environment selection change
  if (environmentSelect) {
    environmentSelect.addEventListener('change', updateApiUrlDisplay);
  }

  // Test API button
  if (testApiBtn) {
    testApiBtn.addEventListener('click', testApiConnection);
  }

  // Save settings button
  if (saveSettingsBtn) {
    saveSettingsBtn.addEventListener('click', saveSettings);
  }

  // Toggle hotkeys button
  if (toggleHotkeysBtn && additionalHotkeys) {
    toggleHotkeysBtn.addEventListener('click', toggleHotkeysVisibility);
  }
});

/**
 * Load settings from storage
 */
function loadSettings() {
  chrome.storage.local.get('settings', (data) => {
    const settings = data.settings || defaultSettings;

    // Set environment
    if (environmentSelect) {
      environmentSelect.value = settings.environment || 'local';
      updateApiUrlDisplay();
    }

    // Set other settings
    if (saveHistoryCheckbox) {
      saveHistoryCheckbox.checked = settings.saveHistory !== false;
    }

    if (useMockApiCheckbox) {
      useMockApiCheckbox.checked = settings.useMockApi === true;
    }
  });
}

/**
 * Toggle API settings visibility based on default environment
 * Hide API settings section when default environment is set to production
 */
function toggleApiSettingsVisibility() {
  if (!apiSettingsHeading || !apiSettingsFormGroup) return;

  const isProdEnvironment = defaultSettings.environment === 'prod';

  // Hide or show API settings section based on default environment
  if (isProdEnvironment) {
    // Hide API settings in production environment
    apiSettingsHeading.style.display = 'none';
    apiSettingsFormGroup.style.display = 'none';
  } else {
    // Show API settings in non-production environments
    apiSettingsHeading.style.display = '';
    apiSettingsFormGroup.style.display = '';
  }
}

/**
 * Update API URL display based on selected environment
 */
function updateApiUrlDisplay() {
  if (currentApiUrlSpan && environmentSelect) {
    const environment = environmentSelect.value;
    currentApiUrlSpan.textContent = apiUrls[environment] || apiUrls.local;
  }
}

/**
 * Save settings to storage
 */
function saveSettings() {
  const settings = {
    environment: environmentSelect ? environmentSelect.value : 'local',
    saveHistory: saveHistoryCheckbox ? saveHistoryCheckbox.checked : true,
    useMockApi: useMockApiCheckbox ? useMockApiCheckbox.checked : false
  };

  chrome.storage.local.set({ settings: settings }, () => {
    showToast('Settings saved');
  });
}

/**
 * Get settings from storage
 * @returns {Promise<Object>} Settings object
 */
function getSettings() {
  return new Promise((resolve) => {
    chrome.storage.local.get('settings', (data) => {
      resolve(data.settings || defaultSettings);
    });
  });
}

/**
 * Test API connection
 */
async function testApiConnection() {
  if (!apiTestResult) return;

  // Get the API URL
  const environment = environmentSelect ? environmentSelect.value : 'local';
  const apiUrl = apiUrls[environment] || apiUrls.local;

  // Update the test result
  apiTestResult.textContent = "Testing connection...";
  apiTestResult.style.color = "var(--text-color)";

  try {
    // Test the ping endpoint
    const response = await fetch(`${apiUrl}/ping`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Origin': window.location.origin,
        'X-Requested-With': 'XMLHttpRequest'
      },
      mode: 'cors',
      credentials: 'omit'
    });

    if (response.ok) {
      const data = await response.json();
      apiTestResult.textContent = `Connection successful! API version: ${data.version}`;
      apiTestResult.style.color = "var(--success-color)";

      // Also test the generate endpoint with a simple request
      try {
        const generateResponse = await fetch(`${apiUrl}/generate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': window.location.origin,
            'X-Requested-With': 'XMLHttpRequest'
          },
          mode: 'cors',
          credentials: 'omit',
          body: JSON.stringify({
            selected_text: "This is a test message.",
            user_intent: "Test the API connection",
            tone: "neutral",
            purpose: "reply"
          })
        });

        if (generateResponse.ok) {
          apiTestResult.textContent += "\nGenerate endpoint is also working!";
        } else {
          apiTestResult.textContent += "\nGenerate endpoint test failed: " +
            generateResponse.status + " " + generateResponse.statusText;
        }
      } catch (generateError) {
        apiTestResult.textContent += "\nGenerate endpoint test error: " + generateError.message;
      }
    } else {
      apiTestResult.textContent = `Connection failed: ${response.status} ${response.statusText}`;
      apiTestResult.style.color = "var(--error-color)";
    }
  } catch (error) {
    // Provide more detailed error information
    let errorMessage = error.message;

    // Check if it's a network error
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      errorMessage = 'Network error: Unable to connect to the API server. Please check if the server is running and accessible.';
    }

    // Check if it's a CORS error
    if (error.message.includes('CORS')) {
      errorMessage = 'CORS error: The API server is not allowing requests from the extension. Please check CORS settings on the server.';
    }

    apiTestResult.textContent = `Connection error: ${errorMessage}`;
    apiTestResult.style.color = "var(--error-color)";
  }
}

/**
 * Toggle visibility of additional hotkeys
 */
function toggleHotkeysVisibility() {
  if (!toggleHotkeysBtn || !additionalHotkeys) return;

  // Toggle the 'hidden' class on the additional hotkeys container
  additionalHotkeys.classList.toggle('hidden');

  // Toggle the 'expanded' class on the button to rotate the icon
  toggleHotkeysBtn.classList.toggle('expanded');

  // Update the button title based on current state
  if (additionalHotkeys.classList.contains('hidden')) {
    toggleHotkeysBtn.title = 'Show all shortcuts';
  } else {
    toggleHotkeysBtn.title = 'Hide additional shortcuts';
  }
}

// ReplyPal Settings API for backward compatibility
const ReplyPalSettings = {
  getApiUrl: function() {
    const environment = environmentSelect ? environmentSelect.value : 'local';
    return apiUrls[environment] || apiUrls.local;
  },

  setEnvironment: function(environment) {
    if (environmentSelect && apiUrls[environment]) {
      environmentSelect.value = environment;
      updateApiUrlDisplay();
    }
  }
};
