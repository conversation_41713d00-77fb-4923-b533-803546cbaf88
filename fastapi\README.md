# ReplyPal Simplified API

A simplified version of the ReplyPal API using FastAPI and direct OpenAI integration (without <PERSON><PERSON><PERSON><PERSON>).

## Features

- FastAPI-based REST API
- Direct AI provider integration (OpenAI, DeepSeek, HuggingFace)
- Dynamic model selection based on environment variables
- Streaming response support
- CORS support for browser access
- Simple, clean implementation
- Uses OpenAI API v1.0.0+ format

## Local Development

### Prerequisites

- Python 3.8+
- pip

### Setup

1. Create a virtual environment:

   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:

   ```
   pip install -r requirements.txt
   ```

3. Edit the `.env` file with your API keys and settings:

   ```
   # Choose your AI provider: openai, deepseek, or huggingface
   AI_PROVIDER=openai

   # OpenAI settings
   OPENAI_API_KEY=your-openai-api-key-here
   OPENAI_API_MODEL=gpt-3.5-turbo

   # DeepSeek settings (if using DeepSeek)
   DEEPSEEK_API_KEY=your-deepseek-api-key-here
   DEEPSEEK_API_BASE=https://api.deepseek.com/v1
   DEEPSEEK_API_MODEL=deepseek-chat

   # HuggingFace settings (if using HuggingFace)
   HUGGINGFACE_API_KEY=your-huggingface-api-key-here
   HUGGINGFACE_MODEL=mistralai/Mistral-7B-Instruct-v0.1
   ```

### Running the API

```
uvicorn main:app --reload
```

The API will be available at http://localhost:8000

### API Documentation

FastAPI automatically generates API documentation:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API Endpoints

### GET /ping

Health check endpoint.

**Response:**

```json
{
  "status": "ok",
  "version": "1.0.0"
}
```

### POST /generate

Generate a smart reply based on selected text and user intent.

**Request:**

```json
{
  "selected_text": "optional string",
  "user_intent": "optional string",
  "tone": "optional string (default: neutral)",
  "purpose": "optional string (default: respond)"
}
```

**Response:**

```json
{
  "response": "AI-generated response"
}
```

### POST /generate_stream

Generate a smart reply with streaming response.

**Request:**
Same as `/generate`

**Response:**
Server-sent events with the following format:

```
data: {"text": "partial response chunk"}
```

Final event includes a done flag:

```
data: {"text": "", "done": true}
```

## Switching AI Providers

You can easily switch between different AI providers by changing the `AI_PROVIDER` setting in the `.env` file:

1. For OpenAI:

   ```
   AI_PROVIDER=openai
   ```

2. For DeepSeek:

   ```
   AI_PROVIDER=deepseek
   ```

3. For HuggingFace (not fully implemented in this version):
   ```
   AI_PROVIDER=huggingface
   ```

Make sure to provide the appropriate API keys for the selected provider.
