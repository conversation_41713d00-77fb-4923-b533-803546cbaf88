# ReplyPal

ReplyPal is a Chrome extension that helps you generate smart replies based on selected text and your intent. It uses AI to create contextually appropriate responses that match your desired tone and purpose.

## Features

- Generate AI-powered responses based on selected text
- Customize tone (friendly, formal, casual, empathetic, professional, neutral, emoji)
- Specify purpose (reply, rewrite, summarize, explain, thank, apologize, suggest, ask)
- Real-time streaming responses
- Edit, regenerate, and copy responses
- Save response history
- Keyboard shortcuts for efficient workflow

## Installation

### Chrome Extension

1. Clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `frontend` folder from this repository
5. The ReplyPal extension should now be installed and visible in your Chrome toolbar

### Backend API

1. Navigate to the `fastapi` folder
2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Create a `.env` file with your API keys (see `.env.example` for reference)
5. Start the API server:
   ```
   uvicorn main:app --reload
   ```

## Usage

### Basic Usage

1. Select text on any webpage
2. Click the ReplyPal icon that appears near your selection, or use the keyboard shortcut (Ctrl+Shift+R)
3. Enter your intent (e.g., "Reply politely to this email")
4. Choose a tone and purpose
5. Click "Generate" or press Ctrl+Enter
6. Edit, regenerate, or copy the response as needed

### Keyboard Shortcuts

ReplyPal supports the following keyboard shortcuts for efficient workflow:

| Shortcut | Action |
|----------|--------|
| Ctrl+Shift+R | Open ReplyPal |
| Ctrl+Enter | Generate Response |
| Esc | Close ReplyPal |
| Ctrl+E | Edit Response |
| Ctrl+R | Regenerate Response |
| Ctrl+C | Copy Response (when focused) |

## Project Structure

- `frontend/`: Chrome extension files
  - `js/`: JavaScript files
  - `css/`: CSS stylesheets
  - `assets/`: Icons and other assets
- `fastapi/`: Backend API server
  - `main.py`: Main API implementation
  - `models/`: AI model integrations

## Configuration

### Extension Settings

The extension settings can be configured in the Settings tab:

- **Environment**: Choose between Local Development, Development Server, or Production
- **Save Response History**: Enable/disable saving your last 10 responses

### API Settings

Configure the API settings in the `.env` file:

```
# Choose your AI provider: openai, deepseek, or huggingface
AI_PROVIDER=openai

# OpenAI settings
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_MODEL=gpt-3.5-turbo

# DeepSeek settings (if using DeepSeek)
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_API_MODEL=deepseek-chat

# HuggingFace settings (if using HuggingFace)
HUGGINGFACE_API_KEY=your-huggingface-api-key-here
HUGGINGFACE_MODEL=mistralai/Mistral-7B-Instruct-v0.1
```

## Development

### Frontend Development

The Chrome extension is built with vanilla JavaScript, HTML, and CSS. To make changes:

1. Modify the files in the `frontend` folder
2. Reload the extension in Chrome by going to `chrome://extensions/` and clicking the refresh icon

### Backend Development

The API server is built with FastAPI. To make changes:

1. Modify the files in the `fastapi` folder
2. The server will automatically reload if you started it with `--reload`

## License

This project is licensed under the MIT License - see the LICENSE file for details.
