document.addEventListener('DOMContentLoaded', () => {
  const selectedTextArea = document.getElementById('selected-text');
  const userIntentLabel = document.querySelector('label[for="user-intent"]');
  const userIntentArea = document.getElementById('user-intent');

  function updateUserIntentUI() {
    const selectedText = selectedTextArea.value.trim();

    // Update required status
    if (!selectedText) {
      userIntentArea.required = true;
      // Add required asterisk if not present
      if (!userIntentLabel.querySelector('.required')) {
        const requiredSpan = document.createElement('span');
        requiredSpan.className = 'required';
        requiredSpan.textContent = ' *';
        userIntentLabel.insertBefore(requiredSpan, userIntentLabel.childNodes[1]);
      }

      // Update label and placeholder for no selected text
      userIntentLabel.childNodes[0].nodeValue = 'Your Message ';
      userIntentArea.placeholder = "What do you want to generate? E.g., 'Write a thank you note to my team'";
    } else {
      userIntentArea.required = false;
      // Remove required asterisk if present
      const requiredSpan = userIntentLabel.querySelector('.required');
      if (requiredSpan) {
        requiredSpan.remove();
      }

      // Update label and placeholder for selected text
      userIntentLabel.childNodes[0].nodeValue = 'Your Intent ';
      userIntentArea.placeholder = "What do you want to say? E.g., 'Reply politely to this email'";
    }
  }

  // Initial check
  updateUserIntentUI();

  // Update on input
  selectedTextArea.addEventListener('input', updateUserIntentUI);
});