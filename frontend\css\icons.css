/* ReplyPal - Custom Icons CSS */

/* Icon Font Base */
.icon {
  font-family: sans-serif;
  font-weight: normal;
  font-style: normal;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Replace Font Awesome classes with our own icon implementations */
.fa-solid, .fa-regular {
  position: relative;
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1;
  vertical-align: middle;
}

/* White icons for header */
.header-bar .fa-solid,
.header-bar .fa-regular,
.header-title .fa-solid,
.header-icons .fa-solid,
.tab-icon-btn .fa-solid,
.header-close-btn .fa-solid {
  filter: brightness(0) invert(1); /* Make icons white */
}

/* Comment/Message Icons */
.fa-comment-dots::before {
  content: "\f4ad"; /* Font Awesome comment-dots */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Info Icon */
.fa-circle-info::before {
  content: "\f05a"; /* Font Awesome circle-info */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Edit/Pen Icons */
.fa-pen-to-square::before {
  content: "\f044"; /* Font Awesome pen-to-square */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Copy Icon */
.fa-copy::before {
  content: "\f0c5"; /* Font Awesome copy */
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
}

/* Regenerate/Refresh Icon */
.fa-arrows-rotate::before {
  content: "\f021"; /* Font Awesome arrows-rotate */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Magic Wand Icon */
.fa-wand-magic-sparkles::before {
  content: "\e2ca"; /* Font Awesome wand-magic-sparkles */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Trash Icon */
.fa-trash-can::before,
.fa-trash::before {
  content: "\f2ed"; /* Font Awesome trash-can */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Arrow Icon */
.fa-arrow-right-to-bracket::before {
  content: "\f090"; /* Font Awesome arrow-right-to-bracket */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Close/X Icon */
.fa-xmark::before {
  content: "\f00d"; /* Font Awesome xmark */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Save/Floppy Disk Icon */
.fa-floppy-disk::before {
  content: "\f0c7"; /* Font Awesome floppy-disk */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Gear/Settings Icon */
.fa-gear::before {
  content: "\f013"; /* Font Awesome gear */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* History/Clock Icon */
.fa-clock-rotate-left::before {
  content: "\f1da"; /* Font Awesome clock-rotate-left */
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

/* Adjust icon sizes and alignment */
i.fa-solid, i.fa-regular {
  font-size: 1.1em;
  margin-right: 0.2em;
}

/* Adjust specific icon sizes */
.logo i {
  font-size: 1.2em;
}

.info-icon i {
  font-size: 0.9em;
}

.action-btn i {
  font-size: 1em;
}

/* Fix alignment for emoji icons */
button i {
  vertical-align: middle;
  line-height: 1;
}

/* Ensure consistent spacing */
i + span, span + i {
  margin-left: 0.3em;
}
