<!DOCTYPE html>
<html>
<head>
  <title>Convert SVG to PNG</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .output {
      margin-top: 20px;
    }
    canvas {
      border: 1px solid #ccc;
      margin: 10px;
    }
  </style>
</head>
<body>
  <h1>SVG to PNG Converter</h1>
  <p>This page will convert the icon.svg file to PNG files of different sizes.</p>
  <p>Right-click on each image and select "Save Image As..." to save them as icon16.png, icon48.png, and icon128.png.</p>
  
  <div class="output">
    <h2>16x16</h2>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <h2>48x48</h2>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <h2>128x128</h2>
    <canvas id="canvas128" width="128" height="128"></canvas>
  </div>
  
  <script>
    // Load the SVG
    const img = new Image();
    img.src = 'icon.svg';
    img.onload = function() {
      // Draw to canvases
      drawToCanvas('canvas16', img, 16);
      drawToCanvas('canvas48', img, 48);
      drawToCanvas('canvas128', img, 128);
    };
    
    function drawToCanvas(canvasId, img, size) {
      const canvas = document.getElementById(canvasId);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, size, size);
    }
  </script>
</body>
</html>
