/**
 * ReplyPal - Main JavaScript
 * Handles core functionality and initialization
 */

// Global state
let currentResponse = '';
let isGenerating = false;
let isEditMode = false;
let selectedText = '';

// DOM Elements
const selectedTextContainer = document.getElementById('selected-text-container');
const selectedTextArea = document.getElementById('selected-text');
const userIntentArea = document.getElementById('user-intent');
const toneSelect = document.getElementById('tone');
const purposeContainer = document.getElementById('purpose-container');
const purposeSelect = document.getElementById('purpose');
const generateBtn = document.getElementById('generate-btn');
const generateBtnSpinner = document.querySelector('.generate-btn-spinner');
const responseContainer = document.getElementById('response-container');
const responseText = document.getElementById('response-text');
const editBtn = document.getElementById('edit-btn');
const regenerateBtn = document.getElementById('regenerate-btn');
const copyBtn = document.getElementById('copy-btn');
const loadingSpinner = document.getElementById('loading-spinner');

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Set up event listeners
  setupEventListeners();

  // Initialize UI state
  initializeUI();

  // Check for selected text
  checkForSelectedText();
});

/**
 * Handle keyboard shortcuts within the popup
 */
function handleKeyboardShortcuts(e) {
  // Ctrl+Enter to generate response from anywhere in the popup
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault();
    console.log('Keyboard shortcut detected: Ctrl+Enter');
    generateResponse();
    return;
  }

  // Escape to close the popup
  if (e.key === 'Escape') {
    e.preventDefault();
    console.log('Keyboard shortcut detected: Escape');
    closePopup();
    return;
  }

  // Only process the following shortcuts if we have a response
  if (currentResponse && !responseContainer.classList.contains('hidden')) {
    // Ctrl+C to copy response
    if (e.ctrlKey && e.key === 'c' && document.activeElement === responseText) {
      // Let the browser handle the copy if text is selected
      if (window.getSelection().toString()) {
        return;
      }
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+C');
      copyToClipboard(currentResponse);
      return;
    }

    // Ctrl+E to edit response
    if (e.ctrlKey && e.key === 'e') {
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+E');
      toggleEditMode();
      return;
    }

    // Ctrl+R to regenerate response
    if (e.ctrlKey && e.key === 'r') {
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+R');
      regenerateResponse();
      return;
    }
  }
}

/**
 * Close the popup
 */
function closePopup() {
  // Send message to parent window to close the popup
  if (window.parent && window !== window.parent) {
    window.parent.postMessage({ action: 'closePopup' }, '*');
  } else {
    // If not in iframe, close the window
    window.close();
  }
}

/**
 * Set up event listeners for UI interactions
 */
function setupEventListeners() {
  // Tab navigation
  document.querySelectorAll('.tab-btn, .tab-icon-btn').forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;
      switchTab(tabName);
    });
  });

  // Close button
  const closeBtn = document.querySelector('.header-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', closePopup);
  }

  // Generate response
  generateBtn.addEventListener('click', generateResponse);

  // Response actions
  editBtn.addEventListener('click', toggleEditMode);
  regenerateBtn.addEventListener('click', regenerateResponse);
  copyBtn.addEventListener('click', () => copyToClipboard(currentResponse));

  // Global keyboard shortcuts
  document.addEventListener('keydown', handleKeyboardShortcuts);

  // Auto-resize textareas
  setupAutoResizeTextareas();
}

/**
 * Initialize UI state
 */
function initializeUI() {
  // Load settings
  loadSettings();

  // Load history
  loadHistory();

  // Set up message listener for parent window communication
  setupMessageListener();
}

/**
 * Check for selected text and update UI accordingly
 */
function checkForSelectedText() {
  // Check if we're in an iframe
  const isInIframe = window !== window.top;

  if (isInIframe) {
    // We're in an iframe, get selected text from storage
    chrome.storage.local.get('selectedText', (data) => {
      selectedText = data.selectedText || '';
      updateUIForSelectedText();
    });
  } else {
    // We're not in an iframe, check for selected text in the current window
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      selectedText = selection.toString().trim();
    }
    updateUIForSelectedText();
  }
}

/**
 * Update UI based on whether text is selected
 */
function updateUIForSelectedText() {
  const userIntentLabel = document.querySelector('label[for="user-intent"]');

  // Get the info icon element
  const userIntentInfoIcon = document.querySelector('label[for="user-intent"] .info-icon');

  if (selectedText) {
    // Text is selected, show the selected text container
    selectedTextContainer.classList.remove('hidden');
    selectedTextArea.value = selectedText;

    // Show purpose dropdown
    purposeContainer.classList.remove('hidden');

    // Update user intent label and placeholder for selected text mode
    if (userIntentLabel) {
      userIntentLabel.childNodes[0].nodeValue = 'Your Intent ';
    }
    var content = "Optional: Describe what you want to say, like 'Reply politely that I cannot attend the event' or 'Write a thank you note'. If you leave it blank, the AI will choose a response based on the selected text, tone, and purpose. Your input will be prioritized.";

    userIntentArea.placeholder = content;
    userIntentArea.title = content;

    // Update the info icon title to match the textarea
    if (userIntentInfoIcon) {
      userIntentInfoIcon.title = content;
    }
  } else {
    // No text selected, hide the selected text container
    selectedTextContainer.classList.add('hidden');

    // Hide purpose dropdown
    purposeContainer.classList.add('hidden');

    // Update user intent label and placeholder for no selected text mode
    if (userIntentLabel) {
      userIntentLabel.childNodes[0].nodeValue = 'Your Message ';
    }
    var content = "Required: Describe what you want the AI to generate, like 'Write a thank you email to my team', 'Create a LinkedIn post about my promotion', 'How do I politely decline a meeting?' or 'Write a whatsapp message to my relative to invite them to dinner.' Your input and the tone guides the response.";
    userIntentArea.placeholder = content;
    userIntentArea.title = content;

    // Update the info icon title to match the textarea
    if (userIntentInfoIcon) {
      userIntentInfoIcon.title = content;
    }
  }

  // Focus on user intent
  userIntentArea.focus();
}

/**
 * Generate a response based on user input
 */
async function generateResponse() {
  // Validate input
  if (!validateInput()) {
    return;
  }

  // Don't allow multiple generations at once
  if (isGenerating) {
    return;
  }

  // Set generating state
  isGenerating = true;
  generateBtn.disabled = true;

  // Show loading spinner
  showLoadingOverlay();

  try {
    // Get request data
    const requestData = {
      selected_text: selectedTextArea.value,
      user_intent: userIntentArea.value,
      tone: toneSelect.value,
      purpose: purposeSelect.value
    };

    // Use streaming API
    await generateStreamingResponse(requestData);

    // Save to history if enabled
    saveToHistory(requestData.user_intent, requestData.selected_text, currentResponse);

    // Show response container
    responseContainer.classList.remove('hidden');

    // Scroll to response
    scrollToResponse();
  } catch (error) {
    console.error('Error generating response:', error);
    showToast(`Error: ${error.message}`, true);

    // Show error in response area
    setResponseText(error.message);
    responseContainer.classList.remove('hidden');
  } finally {
    // Reset generating state
    isGenerating = false;
    generateBtn.disabled = false;

    // Hide loading spinner
    hideLoadingOverlay();
  }
}

/**
 * Validate user input before generating response
 */
function validateInput() {
  // User intent is required only if selected text is empty
  if (!userIntentArea.value.trim() && !selectedTextArea.value.trim()) {
    // Show different error message based on whether selected text is visible
    if (selectedTextContainer.classList.contains('hidden')) {
      showToast('Please enter your message', true);
    } else {
      showToast('Please enter your intent', true);
    }
    userIntentArea.focus();
    return false;
  }
  return true;
}

/**
 * Generate a streaming response
 */
async function generateStreamingResponse(requestData) {
  // Reset response
  setResponseText('');
  currentResponse = '';

  // Set up streaming
  const isInIframe = window !== window.top;

  if (isInIframe) {
    // We're in an iframe, use postMessage to communicate with parent
    await streamFromParent(requestData);
  } else {
    // We're not in an iframe, use fetch directly
    await streamFromAPI(requestData);
  }
}

/**
 * Stream response from parent window
 */
async function streamFromParent(requestData) {
  return new Promise((resolve, reject) => {
    const messageHandler = (event) => {
      if (event.source !== window.parent) return;

      const message = event.data;

      if (message.action === 'streamResponseChunk') {
        if (message.chunk === '[DONE]') {
          window.removeEventListener('message', messageHandler);
          resolve();
        } else {
          appendResponseChunk(message.chunk);
        }
      } else if (message.action === 'streamResponseError') {
        window.removeEventListener('message', messageHandler);
        reject(new Error(message.error || 'Unknown streaming error'));
      }
    };

    window.addEventListener('message', messageHandler);

    // Send request to parent
    window.parent.postMessage({
      action: 'generateStreamingResponse',
      data: requestData
    }, '*');

    // Set timeout
    setTimeout(() => {
      window.removeEventListener('message', messageHandler);
      if (currentResponse.length === 0) {
        reject(new Error('Request timed out'));
      } else {
        resolve();
      }
    }, 60000);
  });
}

/**
 * Stream response directly from API
 */
async function streamFromAPI(requestData) {
  try {
    // Get API URL from settings
    const settings = await getSettings();
    const environment = settings.environment || 'local';
    const apiUrls = {
      local: 'http://localhost:8000',
      dev: 'https://dev-api.replypal.com',
      prod: 'https://api.replypal.com'
    };
    const apiUrl = apiUrls[environment] || apiUrls.local;

    // Set up fetch with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    const response = await fetch(`${apiUrl}/generate_stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      },
      body: JSON.stringify(requestData),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    // Process the stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.substring(6);
          if (data === '[DONE]') return;
          appendResponseChunk(data);
        }
      }
    }
  } catch (error) {
    console.error('Error in streaming fetch:', error);
    throw error;
  }
}

/**
 * Append a chunk to the response
 */
function appendResponseChunk(chunk) {
  // Append chunk to response
  currentResponse += chunk;
  setResponseText(currentResponse);

  // Auto-scroll textarea to bottom
  responseText.scrollTop = responseText.scrollHeight;
}

/**
 * Toggle edit mode for response
 */
function toggleEditMode() {
  if (!currentResponse) return;

  if (isEditMode) {
    // Save changes
    saveResponseChanges();
  } else {
    // Enter edit mode
    enterEditMode();
  }
}

/**
 * Enter edit mode for response
 */
function enterEditMode() {
  // Set edit mode flag
  isEditMode = true;

  // Update button text
  editBtn.innerHTML = '<i class="fa-solid fa-floppy-disk"></i> Save';
  editBtn.title = 'Save Changes';

  // Make response editable
  responseText.removeAttribute('readonly');
  responseText.classList.add('editing');

  // Disable other buttons
  regenerateBtn.disabled = true;
  regenerateBtn.classList.add('disabled');
  copyBtn.disabled = true;
  copyBtn.classList.add('disabled');

  // Focus on response text
  responseText.focus();
}

/**
 * Save response changes
 */
function saveResponseChanges() {
  // Update current response
  currentResponse = responseText.innerText;

  // Make response readonly again
  responseText.setAttribute('readonly', 'readonly');
  responseText.classList.remove('editing');

  // Reset edit mode flag
  isEditMode = false;

  // Update button text
  editBtn.innerHTML = '<i class="fa-solid fa-pen-to-square"></i> Edit';
  editBtn.title = 'Edit Response';

  // Re-enable other buttons
  regenerateBtn.disabled = false;
  regenerateBtn.classList.remove('disabled');
  copyBtn.disabled = false;
  copyBtn.classList.remove('disabled');

  // Show success message
  showToast('Changes saved');

  // Update history if needed
  updateLastHistoryItem(currentResponse);
}

/**
 * Regenerate the response
 */
function regenerateResponse() {
  // If in edit mode, exit it first
  if (isEditMode) {
    isEditMode = false;
    editBtn.innerHTML = '<i class="fa-solid fa-pen-to-square"></i> Edit';
    editBtn.title = 'Edit Response';
    responseText.setAttribute('readonly', 'readonly');
    responseText.classList.remove('editing');
    regenerateBtn.disabled = false;
    regenerateBtn.classList.remove('disabled');
    copyBtn.disabled = false;
    copyBtn.classList.remove('disabled');
  }

  // Generate a new response
  generateResponse();
}

/**
 * Set up message listener for parent window communication
 */
function setupMessageListener() {
  window.addEventListener('message', (event) => {
    // Only accept messages from parent window
    if (event.source !== window.parent) return;

    const message = event.data;

    // Handle clipboard operation result
    if (message.action === 'copyToClipboardResult') {
      if (message.success) {
        showToast('Copied to clipboard!');
      } else {
        showToast('Failed to copy to clipboard', true);
      }
    }
  });
}

/**
 * Set response text
 */
function setResponseText(text) {
  const responseDiv = document.getElementById('response-text');
  if (responseDiv) {
    // Display as HTML, so <br> and other tags are rendered
    responseDiv.innerHTML = text || '';
  }
}

/**
 * Convert HTML content to plain text for clipboard
 * @param {string} htmlText - Text that may contain HTML tags
 * @returns {string} Plain text with proper line breaks
 */
function convertHtmlToPlainText(htmlText) {
  if (!htmlText) return '';

  // Replace <br> and <br/> and <br /> tags with line breaks
  let plainText = htmlText.replace(/<br\s*\/?>/gi, '\n');

  // Replace other common HTML entities if needed
  plainText = plainText.replace(/&nbsp;/gi, ' ');
  plainText = plainText.replace(/&amp;/gi, '&');
  plainText = plainText.replace(/&lt;/gi, '<');
  plainText = plainText.replace(/&gt;/gi, '>');
  plainText = plainText.replace(/&quot;/gi, '"');

  // Remove any remaining HTML tags
  plainText = plainText.replace(/<[^>]*>/g, '');

  return plainText.trim();
}

function copyToClipboard(text) {
  if (!text) return;

  // Convert HTML to plain text for clipboard
  const plainText = convertHtmlToPlainText(text);

  // Use the Clipboard API if available
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(plainText)
      .then(() => showToast('Copied to clipboard!'))
      .catch(() => showToast('Failed to copy to clipboard', true));
  } else {
    // Fallback for older browsers
    const textarea = document.createElement('textarea');
    textarea.value = plainText;
    textarea.setAttribute('readonly', '');
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      showToast('Copied to clipboard!');
    } catch (err) {
      showToast('Failed to copy to clipboard', true);
    }
    document.body.removeChild(textarea);
  }
}
