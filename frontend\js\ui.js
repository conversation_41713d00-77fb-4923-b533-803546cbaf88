/**
 * ReplyPal - UI Utilities
 * Handles UI interactions and utilities
 */

/**
 * Switch to a specific tab
 * @param {string} tabName - The name of the tab to switch to
 */
function switchTab(tabName) {
  // Update active tab button (both old .tab-btn and new .tab-icon-btn)
  document.querySelectorAll('.tab-btn, .tab-icon-btn').forEach(btn => {
    btn.classList.toggle('active', btn.dataset.tab === tabName);
  });

  // Show active tab content
  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.toggle('active', content.id === tabName);
  });

  // Special handling for history tab
  if (tabName === 'history') {
    // Refresh history list
    loadHistory();
  }
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {boolean} isError - Whether this is an error message
 */
function showToast(message, isError = false) {
  const toast = document.getElementById('toast');

  // Clear any existing timeout
  if (toast.timeoutId) {
    clearTimeout(toast.timeoutId);
  }

  // Set message and class
  toast.textContent = message;
  toast.className = 'toast';
  if (isError) {
    toast.classList.add('error');
  }

  // Show toast
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);

  // Hide toast after 3 seconds
  toast.timeoutId = setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);
}

/**
 * Show loading spinners
 */
function showLoadingOverlay() {
  // Show spinner in the generate button
  generateBtnSpinner.classList.add('visible');

  // Show the loading spinner in the response area if needed
  if (!responseContainer.classList.contains('hidden')) {
    loadingSpinner.classList.add('visible');
  }
}

/**
 * Hide loading spinners
 */
function hideLoadingOverlay() {
  // Hide spinner in the generate button
  generateBtnSpinner.classList.remove('visible');

  // Hide the loading spinner in the response area
  loadingSpinner.classList.remove('visible');
}

/**
 * Scroll to the response section
 */
function scrollToResponse() {
  if (responseContainer.classList.contains('hidden')) {
    return;
  }

  // Use requestAnimationFrame for smoother scrolling
  requestAnimationFrame(() => {
    responseContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  });
}

/**
 * Convert HTML content to plain text for clipboard
 * @param {string} htmlText - Text that may contain HTML tags
 * @returns {string} Plain text with proper line breaks
 */
function convertHtmlToPlainText(htmlText) {
  if (!htmlText) return '';

  // Replace <br> and <br/> and <br /> tags with line breaks
  let plainText = htmlText.replace(/<br\s*\/?>/gi, '\n');

  // Replace other common HTML entities if needed
  plainText = plainText.replace(/&nbsp;/gi, ' ');
  plainText = plainText.replace(/&amp;/gi, '&');
  plainText = plainText.replace(/&lt;/gi, '<');
  plainText = plainText.replace(/&gt;/gi, '>');
  plainText = plainText.replace(/&quot;/gi, '"');

  // Remove any remaining HTML tags
  plainText = plainText.replace(/<[^>]*>/g, '');

  return plainText.trim();
}

/**
 * Copy text to clipboard
 * @param {string} text - The text to copy
 */
function copyToClipboard(text) {
  if (!text) return;

  // Convert HTML to plain text for clipboard
  const plainText = convertHtmlToPlainText(text);

  // Check if we're in an iframe
  const isInIframe = window !== window.top;

  if (isInIframe) {
    // We're in an iframe, use the background script
    chrome.runtime.sendMessage({
      action: 'copyToClipboard',
      text: plainText
    }, (response) => {
      if (response && response.success) {
        showToast('Copied to clipboard!');
      } else {
        // Try to communicate with parent window as fallback
        window.parent.postMessage({
          action: 'copyToClipboard',
          text: plainText
        }, '*');
      }
    });
  } else {
    // We're not in an iframe, try direct methods
    tryDirectClipboardMethods(plainText);
  }
}

/**
 * Try direct clipboard methods
 * @param {string} text - The text to copy (should already be converted to plain text)
 */
function tryDirectClipboardMethods(text) {
  // Use the modern Clipboard API if available
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text)
      .then(() => {
        showToast('Copied to clipboard!');
      })
      .catch(err => {
        console.error('Clipboard API failed:', err);
        // Fall back to the extension API
        useExtensionClipboardAPI(text);
      });
  } else {
    // Fall back to the extension API
    useExtensionClipboardAPI(text);
  }
}

/**
 * Use the extension API for clipboard operations
 * @param {string} text - The text to copy (should already be converted to plain text)
 */
function useExtensionClipboardAPI(text) {
  chrome.runtime.sendMessage({
    action: 'copyToClipboard',
    text: text
  }, (response) => {
    if (response && response.success) {
      showToast('Copied to clipboard!');
    } else {
      // Last resort: try the fallback method
      copyToClipboardFallback(text);
    }
  });
}

/**
 * Fallback method for copying to clipboard
 * @param {string} text - The text to copy (should already be converted to plain text)
 */
function copyToClipboardFallback(text) {
  // Create a temporary textarea element
  const textarea = document.createElement('textarea');
  textarea.value = text;

  // Make the textarea non-editable and invisible
  textarea.setAttribute('readonly', '');
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';

  // Append the textarea to the document
  document.body.appendChild(textarea);

  // Select the text in the textarea
  textarea.select();

  try {
    // Execute the copy command
    const success = document.execCommand('copy');
    if (success) {
      showToast('Copied to clipboard!');
    } else {
      showToast('Failed to copy to clipboard', true);
    }
  } catch (err) {
    console.error('Fallback copy method failed:', err);
    showToast('Failed to copy to clipboard', true);
  }

  // Remove the temporary textarea
  document.body.removeChild(textarea);
}

/**
 * Set up auto-resize for textareas
 */
function setupAutoResizeTextareas() {
  const textareas = document.querySelectorAll('textarea');

  textareas.forEach(textarea => {
    // Set initial height
    resizeTextarea(textarea);

    // Add input event listener
    textarea.addEventListener('input', () => {
      resizeTextarea(textarea);
    });
  });
}

/**
 * Resize a textarea to fit its content
 * @param {HTMLTextAreaElement} textarea - The textarea to resize
 */
function resizeTextarea(textarea) {
  // Reset height to auto to get the correct scrollHeight
  textarea.style.height = 'auto';

  // Set height to scrollHeight + border
  const newHeight = textarea.scrollHeight + 2;
  textarea.style.height = `${newHeight}px`;
}

/**
 * Format a date for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
  const date = new Date(dateString);

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return 'Unknown date';
  }

  // Format date
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Check if date is today or yesterday
  if (date >= today) {
    return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (date >= yesterday) {
    return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) +
           ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  }
}

/**
 * Truncate text to a specific length
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} Truncated text
 */
function truncateText(text, maxLength) {
  if (!text) return '';

  if (text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength) + '...';
}

// Handle textarea resizing
function initializeResizableTextareas() {
    const textareas = document.querySelectorAll('textarea');

    textareas.forEach(textarea => {
        // Set initial size
        textarea.style.width = '100%';

        // Add input event listener to auto-resize height
        textarea.addEventListener('input', () => {
            // Reset height to auto to get the correct scrollHeight
            textarea.style.height = 'auto';

            // Set height to scrollHeight + border
            const newHeight = textarea.scrollHeight + 2;
            textarea.style.height = `${newHeight}px`;
        });

        // Initial resize
        textarea.dispatchEvent(new Event('input'));
    });
}

// Initialize UI components
document.addEventListener('DOMContentLoaded', () => {
    // Initialize resizable textareas
    initializeResizableTextareas();

    // Add window resize handler
    window.addEventListener('resize', () => {
        // Ensure textareas maintain proper width
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.style.width = '100%';
        });
    });

    // Tab navigation for new icon buttons
    document.querySelectorAll('.tab-icon-btn').forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('.tab-icon-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        const tabName = button.dataset.tab;
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.toggle('active', content.id === tabName);
        });
      });
    });
});
