/**
 * ReplyPal - History Management
 * Handles saving, loading, and managing response history
 */

// DOM Elements
const historyList = document.getElementById('history-list');
const clearHistoryBtn = document.getElementById('clear-history-btn');

// Set up event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Clear history button
  if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener('click', clearHistory);
  }
});

/**
 * Load history from storage
 */
function loadHistory() {
  chrome.storage.local.get('history', (data) => {
    const history = data.history || [];

    if (history.length === 0) {
      // No history items
      historyList.innerHTML = '<p class="empty-message">Your history will appear here.</p>';

      // Hide clear history button
      if (clearHistoryBtn) {
        clearHistoryBtn.style.display = 'none';
      }
      return;
    }

    // Show clear history button
    if (clearHistoryBtn) {
      clearHistoryBtn.style.display = 'block';
    }

    // Clear history list
    historyList.innerHTML = '';

    // Add history items
    history.forEach(item => {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-item';

      historyItem.innerHTML = `
        <div class="history-item-header">
          <span class="history-item-title">${truncateText(item.intent, 30)}</span>
          <span class="history-item-date">${formatDate(item.date)}</span>
        </div>
        <div class="history-item-content">${truncateText(item.response, 100)}</div>
        <div class="history-item-actions">
          <button class="action-btn use-btn" data-id="${item.id}">
            <i class="fa-solid fa-arrow-right-to-bracket"></i> Use
          </button>
          <button class="action-btn copy-btn" data-id="${item.id}">
            <i class="fa-regular fa-copy"></i> Copy
          </button>
          <button class="action-btn delete-btn" data-id="${item.id}">
            <i class="fa-solid fa-trash"></i> Delete
          </button>
        </div>
      `;

      historyList.appendChild(historyItem);
    });

    // Add event listeners to history item buttons
    addHistoryItemEventListeners();
  });
}

/**
 * Add event listeners to history item buttons
 */
function addHistoryItemEventListeners() {
  // Use buttons
  document.querySelectorAll('.use-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const id = parseInt(e.target.closest('.use-btn').dataset.id);
      useHistoryItem(id);
    });
  });

  // Copy buttons
  document.querySelectorAll('.copy-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const id = parseInt(e.target.closest('.copy-btn').dataset.id);
      copyHistoryItem(id);
    });
  });

  // Delete buttons
  document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const id = parseInt(e.target.closest('.delete-btn').dataset.id);
      deleteHistoryItem(id);
    });
  });
}

/**
 * Save response to history
 * @param {string} intent - User intent
 * @param {string} selectedText - Selected text
 * @param {string} response - Generated response
 */
function saveToHistory(intent, selectedText, response) {
  // Check if history saving is enabled
  chrome.storage.local.get('settings', (data) => {
    const settings = data.settings || {};

    if (settings.saveHistory === false) {
      return;
    }

    chrome.storage.local.get('history', (data) => {
      const history = data.history || [];

      // Add new item to history
      history.unshift({
        id: Date.now(),
        date: new Date().toISOString(),
        intent: intent,
        selectedText: selectedText,
        response: response
      });

      // Limit history to 10 items
      if (history.length > 10) {
        history.pop();
      }

      // Save updated history
      chrome.storage.local.set({ history: history }, () => {
        // Refresh history list if history tab is active
        if (document.getElementById('history').classList.contains('active')) {
          loadHistory();
        }
      });
    });
  });
}

/**
 * Update the last history item
 * @param {string} newResponse - New response text
 */
function updateLastHistoryItem(newResponse) {
  chrome.storage.local.get('history', (data) => {
    if (!data.history || data.history.length === 0) return;

    const history = data.history;
    history[0].response = newResponse;

    chrome.storage.local.set({ history: history }, () => {
      // Refresh history list if history tab is active
      if (document.getElementById('history').classList.contains('active')) {
        loadHistory();
      }
    });
  });
}

/**
 * Use a history item
 * @param {number} id - History item ID
 */
function useHistoryItem(id) {
  chrome.storage.local.get('history', (data) => {
    const history = data.history || [];
    const item = history.find(item => item.id === id);

    if (item) {
      // If in edit mode, exit it first
      if (isEditMode) {
        isEditMode = false;
        document.getElementById('edit-btn').innerHTML = '<i class="fa-solid fa-pen-to-square"></i> Edit';
        document.getElementById('edit-btn').title = 'Edit Response';
      }

      // Fill in the form
      document.getElementById('selected-text').value = item.selectedText || '';
      document.getElementById('user-intent').value = item.intent || '';

      // Check if the history item has selected text data
      const hasSelectedText = item.selectedText && item.selectedText.trim() !== '';

      // Update the user intent label and placeholder based on selected text
      const userIntentLabel = document.querySelector('label[for="user-intent"]');
      const userIntentArea = document.getElementById('user-intent');

      if (hasSelectedText) {
        // Show the selected text container
        const selectedTextContainer = document.getElementById('selected-text-container');
        if (selectedTextContainer) {
          selectedTextContainer.classList.remove('hidden');
        }

        // Show the purpose dropdown
        const purposeContainer = document.getElementById('purpose-container');
        if (purposeContainer) {
          purposeContainer.classList.remove('hidden');
        }

        // Update label and placeholder for selected text mode
        if (userIntentLabel) {
          userIntentLabel.childNodes[0].nodeValue = 'Your Intent ';
        }
        if (userIntentArea) {
          userIntentArea.placeholder = "What do you want to say? E.g., 'Reply politely to this email'";
        }
      } else {
        // Hide the selected text container
        const selectedTextContainer = document.getElementById('selected-text-container');
        if (selectedTextContainer) {
          selectedTextContainer.classList.add('hidden');
        }

        // Hide the purpose dropdown
        const purposeContainer = document.getElementById('purpose-container');
        if (purposeContainer) {
          purposeContainer.classList.add('hidden');
        }

        // Update label and placeholder for no selected text mode
        if (userIntentLabel) {
          userIntentLabel.childNodes[0].nodeValue = 'Your Message ';
        }
        if (userIntentArea) {
          userIntentArea.placeholder = "What do you want to generate? E.g., 'Write a thank you note to my team'";
        }
      }

      // Set the response
      currentResponse = item.response;

      // Use setResponseText function if available, otherwise set innerHTML directly
      if (typeof setResponseText === 'function') {
        setResponseText(currentResponse);
      } else {
        const responseElement = document.getElementById('response-text');
        if (responseElement) {
          responseElement.innerHTML = currentResponse;
        }
      }

      // Make sure response is readonly
      const responseElement = document.getElementById('response-text');
      if (responseElement) {
        responseElement.setAttribute('readonly', 'readonly');
        responseElement.classList.remove('editing');
      }

      // Show the response container
      document.getElementById('response-container').classList.remove('hidden');

      // Re-enable buttons that might have been disabled in edit mode
      const regenerateBtn = document.getElementById('regenerate-btn');
      const copyBtn = document.getElementById('copy-btn');
      if (regenerateBtn) {
        regenerateBtn.disabled = false;
        regenerateBtn.classList.remove('disabled');
      }
      if (copyBtn) {
        copyBtn.disabled = false;
        copyBtn.classList.remove('disabled');
      }

      // Switch to compose tab
      switchTab('compose');

      // Scroll to response
      scrollToResponse();
    }
  });
}

/**
 * Copy a history item to clipboard
 * @param {number} id - History item ID
 */
function copyHistoryItem(id) {
  chrome.storage.local.get('history', (data) => {
    const history = data.history || [];
    const item = history.find(item => item.id === id);

    if (item) {
      // The copyToClipboard function will handle HTML to plain text conversion
      copyToClipboard(item.response);
    }
  });
}

/**
 * Delete a history item
 * @param {number} id - History item ID
 */
function deleteHistoryItem(id) {
  chrome.storage.local.get('history', (data) => {
    const history = data.history || [];
    const updatedHistory = history.filter(item => item.id !== id);

    chrome.storage.local.set({ history: updatedHistory }, () => {
      loadHistory();
    });
  });
}

/**
 * Clear all history
 */
function clearHistory() {
  if (confirm('Are you sure you want to clear all history?')) {
    chrome.storage.local.set({ history: [] }, () => {
      loadHistory();
      showToast('History cleared');
    });
  }
}
